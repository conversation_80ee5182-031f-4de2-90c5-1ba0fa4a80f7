@echo off
echo ========================================
echo    FAST MSG File Import to Outlook
echo    Python Multiprocessing Version
echo ========================================
echo.
echo This script will:
echo   1. Analyze all MSG files using multiprocessing (FAST)
echo   2. Import only email files with controlled delays (SAFE)
echo.
echo Make sure:
echo   - Python is installed
echo   - Outlook is running
echo   - You have the required Python packages
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

echo Python found. Checking/installing requirements...
pip install -r requirements.txt

echo.
echo ========================================
echo STEP 1: FAST ANALYSIS (Multiprocessing)
echo ========================================
echo.

REM Get MSG folder path from user
set /p MSG_FOLDER="Enter the full path to your MSG files folder: "

REM Validate folder exists
if not exist "%MSG_FOLDER%" (
    echo ERROR: Folder does not exist: %MSG_FOLDER%
    pause
    exit /b 1
)

echo.
echo Starting fast analysis of MSG files...
echo This will use multiple CPU cores for maximum speed.
echo.

REM Run the analysis
python analyze_msg_files.py "%MSG_FOLDER%"

REM Check if analysis was successful
if errorlevel 1 (
    echo ERROR: Analysis failed. Check the log file for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo STEP 2: CONTROLLED IMPORT (Sequential)
echo ========================================
echo.

REM Get Outlook folder name from user
set /p OUTLOOK_FOLDER="Enter the name for the Outlook folder (e.g., My_Imported_Emails): "

REM Get delay preference
echo.
echo Delay between imports (recommended: 0.5 seconds):
echo   - 0.1 = Very fast (may overwhelm Outlook)
echo   - 0.5 = Balanced (recommended)
echo   - 1.0 = Conservative (slower but very safe)
echo.
set /p DELAY="Enter delay in seconds (default 0.5): "
if "%DELAY%"=="" set DELAY=0.5

echo.
echo Starting controlled import of email files...
echo This will import files one by one with %DELAY% second delays.
echo.

REM Run the import
python import_email_files.py msg_analysis_results.json "%OUTLOOK_FOLDER%" %DELAY%

REM Check if import was successful
if errorlevel 1 (
    echo ERROR: Import failed. Check the log file for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo IMPORT COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Check your Outlook for the imported emails.
echo.
echo Log files created:
echo   - msg_analysis.log (analysis details)
echo   - msg_import.log (import details)
echo   - msg_analysis_results.json (analysis results)
echo   - import_results_*.json (import results)
echo.
pause

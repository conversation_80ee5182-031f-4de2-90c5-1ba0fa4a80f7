@echo off
echo ========================================
echo    FAST MSG File Import to Outlook
echo    Python Multiprocessing Version
echo ========================================
echo.
echo This script will:
echo   1. Ask for your settings upfront
echo   2. Analyze all MSG files using multiprocessing (FAST)
echo   3. Import only email files with controlled delays (SAFE)
echo   4. Preserve original email dates in Outlook
echo.
echo Make sure:
echo   - Python is installed
echo   - Outlook is running
echo   - You have the required Python packages
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

echo Python found. Checking/installing requirements...
pip install -r requirements.txt

echo.
echo ========================================
echo STARTING FAST MSG IMPORT PROCESS
echo ========================================
echo.
echo The script will ask for all settings upfront, then:
echo   - Analyze files using multiple CPU cores
echo   - Import with proper date preservation
echo.

REM Run the complete process
python analyze_msg_files.py

REM Check if process was successful
if errorlevel 1 (
    echo ERROR: Process failed. Check the log files for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo IMPORT COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo ✅ Check your Outlook for the imported emails with original dates!
echo.
echo Log files created:
echo   - msg_analysis.log (analysis details)
echo   - msg_import.log (import details)
echo   - msg_analysis_results.json (analysis results)
echo   - import_results_*.json (import results)
echo.
echo 📧 Email dates in Outlook will show:
echo   - Sent Items: Original sent date/time
echo   - Inbox Items: Original received date/time
echo.
pause

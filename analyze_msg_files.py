#!/usr/bin/env python3
"""
Fast MSG File Analyzer using Multiprocessing
Analyzes MSG files to determine email vs non-email types with complete content analysis
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from multiprocessing import Pool, cpu_count, Manager
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
import win32com.client
import pythoncom
from typing import Dict, List, Tuple, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('msg_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MSGAnalyzer:
    """Analyzes MSG files to determine if they contain email conversations"""
    
    def __init__(self):
        self.non_email_patterns = [
            "IPM.Appointment",
            "IPM.Schedule.Meeting", 
            "IPM.Task",
            "IPM.Contact",
            "IPM.DistList",
            "IPM.StickyNote", 
            "IPM.Post",
            "IPM.Note.Rules",
            "IPM.Note.Receipt",
            "IPM.Note.Delivery",
            "IPM.Note.NonDelivery",
            "IPM.Note.Read",
            "IPM.Note.Unread",
            "IPM.OLE.Class"
        ]
    
    def is_non_email_type(self, message_class: str) -> bool:
        """Check if message class indicates non-email type"""
        if not message_class:
            return False
        
        for pattern in self.non_email_patterns:
            if message_class.startswith(pattern):
                return True
        return False
    
    def analyze_single_msg_file(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze a single MSG file completely - main item + all embedded items
        Returns comprehensive analysis result
        """
        result = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'has_email_conversations': False,
            'is_non_email_type': False,
            'email_items': [],
            'non_email_items': [],
            'skip_reasons': [],
            'total_items': 0,
            'analysis_time': 0,
            'error': None
        }
        
        start_time = time.time()
        
        try:
            # Initialize COM for this process
            pythoncom.CoInitialize()
            
            # Create Outlook application
            outlook = win32com.client.Dispatch("Outlook.Application")
            namespace = outlook.GetNamespace("MAPI")
            
            # Open the MSG file
            mail_item = namespace.OpenSharedItem(file_path)
            
            # Analyze main item
            main_message_class = getattr(mail_item, 'MessageClass', '')
            main_subject = getattr(mail_item, 'Subject', '')
            main_body = getattr(mail_item, 'Body', '')
            
            logger.debug(f"Analyzing {result['file_name']}: {main_message_class}")
            
            result['total_items'] += 1
            
            # Check if main item is non-email type
            if self.is_non_email_type(main_message_class):
                result['is_non_email_type'] = True
                result['non_email_items'].append(f"Main item: {main_message_class}")
                result['skip_reasons'].append(f"Non-email MSG type detected: {main_message_class}")
                logger.debug(f"  -> Non-email type detected: {main_message_class}")
            else:
                # Check if main item is an email conversation
                if (main_message_class == "IPM.Note" or 
                    main_message_class.startswith("IPM.Note.SMIME") or 
                    main_message_class.startswith("IPM.Note.Secure")):
                    
                    if main_body and main_body.strip():
                        result['has_email_conversations'] = True
                        result['email_items'].append({
                            'message_class': main_message_class,
                            'subject': main_subject,
                            'is_main': True,
                            'has_body': bool(main_body and main_body.strip())
                        })
                        logger.debug(f"  -> Found email conversation in main item")
                    else:
                        result['non_email_items'].append("Main item: Empty email body")
                        logger.debug(f"  -> Empty email body in main item")
                else:
                    result['non_email_items'].append(f"Main item: {main_message_class}")
                    logger.debug(f"  -> Non-email main item: {main_message_class}")
            
            # Only analyze embedded items if main item is not already non-email type
            if not result['is_non_email_type']:
                try:
                    attachment_count = getattr(mail_item, 'Attachments', None)
                    if attachment_count and attachment_count.Count > 0:
                        logger.debug(f"  -> Found {attachment_count.Count} attachments, checking embedded items...")
                        
                        for i in range(1, attachment_count.Count + 1):
                            try:
                                attachment = attachment_count.Item(i)
                                attachment_type = getattr(attachment, 'Type', None)
                                
                                # Type 5 = olEmbeddeditem (embedded MSG item)
                                if attachment_type == 5:
                                    try:
                                        embedded_item = attachment.EmbeddedItem
                                        if embedded_item:
                                            embedded_class = getattr(embedded_item, 'MessageClass', '')
                                            embedded_subject = getattr(embedded_item, 'Subject', '')
                                            embedded_body = getattr(embedded_item, 'Body', '')
                                            
                                            result['total_items'] += 1
                                            logger.debug(f"    -> Embedded item: {embedded_class}")
                                            
                                            # Check embedded item type
                                            if self.is_non_email_type(embedded_class):
                                                result['non_email_items'].append(f"Embedded: {embedded_class}")
                                            elif (embedded_class == "IPM.Note" or 
                                                  embedded_class.startswith("IPM.Note.SMIME") or 
                                                  embedded_class.startswith("IPM.Note.Secure")):
                                                
                                                if embedded_body and embedded_body.strip():
                                                    result['has_email_conversations'] = True
                                                    result['email_items'].append({
                                                        'message_class': embedded_class,
                                                        'subject': embedded_subject,
                                                        'is_main': False,
                                                        'has_body': bool(embedded_body and embedded_body.strip())
                                                    })
                                                    logger.debug(f"    -> Found email conversation in embedded item")
                                                else:
                                                    result['non_email_items'].append("Embedded: Empty email body")
                                            else:
                                                result['non_email_items'].append(f"Embedded: {embedded_class}")
                                        
                                        # Clean up embedded item
                                        del embedded_item
                                    except Exception as e:
                                        logger.debug(f"    -> Error accessing embedded item: {e}")
                                        result['non_email_items'].append(f"Embedded: Error accessing item")
                                
                                # Clean up attachment
                                del attachment
                            except Exception as e:
                                logger.debug(f"    -> Error processing attachment {i}: {e}")
                                continue
                except Exception as e:
                    logger.debug(f"  -> Error processing attachments: {e}")
            
            # Determine final result
            if result['is_non_email_type']:
                skip_reason = "Non-email MSG type detected: " + ", ".join(result['non_email_items'])
                if skip_reason not in result['skip_reasons']:
                    result['skip_reasons'].append(skip_reason)
            elif result['has_email_conversations']:
                logger.debug(f"  -> RESULT: Will IMPORT - contains email conversations")
            else:
                skip_reason = "No email conversations found. Contains: " + ", ".join(result['non_email_items'])
                result['skip_reasons'].append(skip_reason)
            
            # Clean up
            mail_item.Close(0)  # Close without saving
            del mail_item
            del namespace
            del outlook
            
        except Exception as e:
            error_msg = f"Error analyzing {file_path}: {str(e)}"
            logger.error(error_msg)
            result['error'] = error_msg
            result['skip_reasons'].append(f"Analysis error: {str(e)}")
        
        finally:
            try:
                pythoncom.CoUninitialize()
            except:
                pass
        
        result['analysis_time'] = time.time() - start_time
        return result

def analyze_msg_file_worker(file_path: str) -> Dict[str, Any]:
    """Worker function for multiprocessing"""
    analyzer = MSGAnalyzer()
    return analyzer.analyze_single_msg_file(file_path)

def find_msg_files(folder_path: str) -> List[str]:
    """Find all MSG files in the specified folder and subfolders"""
    msg_files = []
    folder = Path(folder_path)
    
    if not folder.exists():
        raise FileNotFoundError(f"Folder does not exist: {folder_path}")
    
    for msg_file in folder.rglob("*.msg"):
        if msg_file.is_file():
            msg_files.append(str(msg_file))
    
    return msg_files

def analyze_msg_files_parallel(folder_path: str, max_workers: int = None) -> Tuple[List[Dict], Dict]:
    """
    Analyze all MSG files in parallel using multiprocessing
    Returns: (analysis_results, summary_stats)
    """
    if max_workers is None:
        # Use 75% of available CPU cores, minimum 1, maximum 8
        max_workers = max(1, min(8, int(cpu_count() * 0.75)))
    
    logger.info(f"Starting MSG file analysis with {max_workers} workers...")
    
    # Find all MSG files
    logger.info("Scanning for MSG files...")
    msg_files = find_msg_files(folder_path)
    total_files = len(msg_files)
    
    if total_files == 0:
        logger.warning("No MSG files found!")
        return [], {}
    
    logger.info(f"Found {total_files} MSG files to analyze")
    
    # Analyze files in parallel
    results = []
    start_time = time.time()
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_file = {executor.submit(analyze_msg_file_worker, file_path): file_path 
                         for file_path in msg_files}
        
        # Process completed tasks
        completed = 0
        for future in as_completed(future_to_file):
            file_path = future_to_file[future]
            try:
                result = future.result()
                results.append(result)
                completed += 1
                
                # Progress update every 10 files or at milestones
                if completed % 10 == 0 or completed in [1, 5, total_files]:
                    elapsed = time.time() - start_time
                    rate = completed / elapsed if elapsed > 0 else 0
                    eta = (total_files - completed) / rate if rate > 0 else 0
                    logger.info(f"Progress: {completed}/{total_files} ({completed/total_files*100:.1f}%) "
                              f"- Rate: {rate:.1f} files/sec - ETA: {eta:.0f}s")
                
            except Exception as e:
                logger.error(f"Failed to analyze {file_path}: {e}")
                # Add error result
                results.append({
                    'file_path': file_path,
                    'file_name': os.path.basename(file_path),
                    'has_email_conversations': False,
                    'is_non_email_type': False,
                    'email_items': [],
                    'non_email_items': [],
                    'skip_reasons': [f"Analysis failed: {str(e)}"],
                    'total_items': 0,
                    'analysis_time': 0,
                    'error': str(e)
                })
                completed += 1
    
    total_time = time.time() - start_time
    
    # Generate summary statistics
    email_files = [r for r in results if r['has_email_conversations']]
    non_email_files = [r for r in results if r['is_non_email_type']]
    error_files = [r for r in results if r['error']]
    skipped_files = [r for r in results if not r['has_email_conversations'] and not r['error']]
    
    summary = {
        'total_files': total_files,
        'email_files': len(email_files),
        'non_email_files': len(non_email_files),
        'skipped_files': len(skipped_files),
        'error_files': len(error_files),
        'analysis_time': total_time,
        'files_per_second': total_files / total_time if total_time > 0 else 0,
        'workers_used': max_workers
    }
    
    logger.info(f"Analysis completed in {total_time:.2f} seconds ({summary['files_per_second']:.1f} files/sec)")
    logger.info(f"Results: {len(email_files)} email files, {len(non_email_files)} non-email files, "
               f"{len(skipped_files)} skipped, {len(error_files)} errors")
    
    return results, summary

def save_analysis_results(results: List[Dict], summary: Dict, output_file: str = "msg_analysis_results.json"):
    """Save analysis results to JSON file"""
    output_data = {
        'summary': summary,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'results': results
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Analysis results saved to: {output_file}")
    return output_file

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python analyze_msg_files.py <msg_folder_path> [max_workers]")
        print("Example: python analyze_msg_files.py C:\\MyEmails\\MSG_Files 4")
        sys.exit(1)
    
    folder_path = sys.argv[1]
    max_workers = int(sys.argv[2]) if len(sys.argv) > 2 else None
    
    try:
        # Analyze files
        results, summary = analyze_msg_files_parallel(folder_path, max_workers)
        
        # Save results
        output_file = save_analysis_results(results, summary)
        
        # Print summary
        print(f"\n{'='*50}")
        print("MSG FILE ANALYSIS SUMMARY")
        print(f"{'='*50}")
        print(f"Total files analyzed: {summary['total_files']}")
        print(f"Email files (will import): {summary['email_files']}")
        print(f"Non-email files (will skip): {summary['non_email_files']}")
        print(f"Other skipped files: {summary['skipped_files']}")
        print(f"Error files: {summary['error_files']}")
        print(f"Analysis time: {summary['analysis_time']:.2f} seconds")
        print(f"Processing rate: {summary['files_per_second']:.1f} files/second")
        print(f"Workers used: {summary['workers_used']}")
        print(f"\nResults saved to: {output_file}")
        print(f"Log file: msg_analysis.log")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

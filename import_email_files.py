#!/usr/bin/env python3
"""
Controlled Sequential MSG File Import to Outlook
Imports only email files identified by the analyzer with proper delays to prevent crashes

IMPORTANT NOTE ABOUT DATES:
- Outlook COM API has limitations when setting dates on newly created items
- SentOn and ReceivedTime may be overridden by Outlook with current timestamp
- Original dates are ALWAYS preserved in the email body header for reference
- This is a known limitation of Outlook's COM interface, not a script issue
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any
import win32com.client
import pythoncom

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('msg_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OutlookImporter:
    """Handles controlled import of MSG files to Outlook"""
    
    def __init__(self, outlook_folder_name: str, delay_between_imports: float = 0.5):
        self.outlook_folder_name = outlook_folder_name
        self.delay_between_imports = delay_between_imports
        self.outlook = None
        self.namespace = None
        self.main_import_folder = None
        self.folder_cache = {}
        
    def initialize_outlook(self):
        """Initialize Outlook COM objects"""
        try:
            pythoncom.CoInitialize()
            self.outlook = win32com.client.Dispatch("Outlook.Application")
            self.namespace = self.outlook.GetNamespace("MAPI")
            
            # Get the default store
            default_store = self.namespace.DefaultStore
            root_folder = default_store.GetRootFolder()
            
            # Create or get the main import folder
            self.main_import_folder = self.ensure_outlook_folder(root_folder, self.outlook_folder_name)
            if not self.main_import_folder:
                raise Exception("Failed to create main import folder")
                
            logger.info(f"Outlook initialized. Main folder: {self.outlook_folder_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Outlook: {e}")
            return False
    
    def ensure_outlook_folder(self, parent_folder, folder_name: str):
        """Create folder if it doesn't exist, with caching"""
        cache_key = f"{parent_folder.Name}\\{folder_name}"
        
        if cache_key in self.folder_cache:
            return self.folder_cache[cache_key]
        
        try:
            # Check if folder exists
            for folder in parent_folder.Folders:
                if folder.Name == folder_name:
                    self.folder_cache[cache_key] = folder
                    return folder
            
            # Create new folder
            logger.info(f"Creating folder: {folder_name}")
            new_folder = parent_folder.Folders.Add(folder_name)
            self.folder_cache[cache_key] = new_folder
            return new_folder
            
        except Exception as e:
            logger.error(f"Error creating folder {folder_name}: {e}")
            return None
    
    def get_msg_classification(self, mail_item) -> str:
        """Determine folder classification for the email"""
        try:
            message_class = getattr(mail_item, 'MessageClass', 'IPM.Note')
            
            # Classification mapping
            classification_map = {
                "IPM.Note": "Inbox",
                "IPM.Note.SMIME": "Inbox", 
                "IPM.Note.Secure": "Inbox",
                "IPM.Schedule.Meeting.Request": "Calendar",
                "IPM.Schedule.Meeting.Resp": "Calendar",
                "IPM.Schedule.Meeting.Canceled": "Calendar",
                "IPM.Appointment": "Calendar",
                "IPM.Task": "Tasks",
                "IPM.Contact": "Contacts",
                "IPM.DistList": "Contacts",
                "IPM.StickyNote": "Notes",
                "IPM.Post": "Posts",
                "IPM.Note.Rules": "Rules",
                "IPM.Note.Receipt": "Receipts",
                "IPM.Note.Delivery": "Receipts",
                "IPM.Note.NonDelivery": "Receipts",
                "IPM.Note.Read": "Receipts",
                "IPM.Note.Unread": "Receipts",
                "IPM.Note.Custom": "Custom",
                "IPM.OLE.Class": "Attachments"
            }
            
            # Check exact match first
            if message_class in classification_map:
                return classification_map[message_class]
            
            # Check pattern matches
            for pattern, folder in classification_map.items():
                if message_class.startswith(pattern):
                    return folder
            
            # For regular emails, try to determine folder based on properties
            try:
                # Check if it's in Sent Items
                if getattr(mail_item, 'Sent', False):
                    return "Sent Items"
                
                # Check if it's a draft
                if not getattr(mail_item, 'Sent', True) and not getattr(mail_item, 'ReceivedTime', None):
                    return "Drafts"
                
            except:
                pass
            
            # Default to Inbox
            return "Inbox"
            
        except Exception as e:
            logger.warning(f"Error determining classification: {e}")
            return "Inbox"
    
    def import_single_msg_file(self, file_path: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """Import a single MSG file to Outlook with proper error handling and original dates"""
        result = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'success': False,
            'classification': None,
            'original_date': None,
            'date_type': None,
            'error': None,
            'import_time': 0
        }

        start_time = time.time()

        try:
            # Open the MSG file
            mail_item = self.namespace.OpenSharedItem(file_path)

            # Get classification
            classification = self.get_msg_classification(mail_item)
            result['classification'] = classification

            # Create or get target folder
            target_folder = self.ensure_outlook_folder(self.main_import_folder, classification)
            if not target_folder:
                raise Exception(f"Failed to create/access folder: {classification}")

            # Create new mail item in target folder
            new_item = target_folder.Items.Add(0)  # 0 = olMailItem

            # Copy basic properties
            new_item.Subject = getattr(mail_item, 'Subject', 'Imported MSG File')

            # Determine the correct original date and type
            original_date = None
            date_type = None
            is_sent_item = False

            try:
                # Check if this is a sent item first
                if hasattr(mail_item, 'Sent') and mail_item.Sent:
                    is_sent_item = True
                    if hasattr(mail_item, 'SentOn') and mail_item.SentOn:
                        original_date = mail_item.SentOn
                        date_type = "SentOn"
                    elif hasattr(mail_item, 'CreationTime') and mail_item.CreationTime:
                        original_date = mail_item.CreationTime
                        date_type = "CreationTime (Sent)"
                else:
                    # This is a received item
                    if hasattr(mail_item, 'ReceivedTime') and mail_item.ReceivedTime:
                        original_date = mail_item.ReceivedTime
                        date_type = "ReceivedTime"
                    elif hasattr(mail_item, 'SentOn') and mail_item.SentOn:
                        original_date = mail_item.SentOn
                        date_type = "SentOn (Received)"
                    elif hasattr(mail_item, 'CreationTime') and mail_item.CreationTime:
                        original_date = mail_item.CreationTime
                        date_type = "CreationTime (Received)"

                result['original_date'] = str(original_date) if original_date else None
                result['date_type'] = date_type

            except Exception as e:
                logger.warning(f"Error determining date for {result['file_name']}: {e}")

            # Add header to body with original information
            original_body = getattr(mail_item, 'Body', '')
            date_info = f"Original Date ({date_type}): {original_date}\r\n" if original_date else "Original Date: Not available\r\n"

            new_item.Body = (
                "=== IMPORTED MSG FILE ===\r\n"
                f"Original MessageClass: {getattr(mail_item, 'MessageClass', 'Unknown')}\r\n"
                f"{date_info}"
                f"Classification: {classification}\r\n"
                f"Original File: {result['file_name']}\r\n"
                f"Import Type: {'Sent Item' if is_sent_item else 'Received Item'}\r\n"
                "Note: This MSG file contained email conversations and has been imported as a single item\r\n"
                "=========================\r\n\r\n"
                f"{original_body}"
            )

            # Copy other properties with error handling
            try:
                # Copy sender/recipient information
                if is_sent_item:
                    # This is a sent item - copy sent item properties
                    if hasattr(mail_item, 'SentOnBehalfOfName') and mail_item.SentOnBehalfOfName:
                        new_item.SentOnBehalfOfName = mail_item.SentOnBehalfOfName
                    if hasattr(mail_item, 'Recipients') and mail_item.Recipients.Count > 0:
                        # Copy recipients
                        for i in range(1, mail_item.Recipients.Count + 1):
                            recipient = mail_item.Recipients.Item(i)
                            new_recipient = new_item.Recipients.Add(recipient.Address)
                            new_recipient.Type = recipient.Type
                        new_item.Recipients.ResolveAll()

                    # NOTE: Setting SentOn for new items may not work reliably
                    # Outlook often overrides this with current time when saving
                    # The original date is preserved in the email body header
                    try:
                        if original_date:
                            new_item.SentOn = original_date
                            logger.debug(f"Attempted to set SentOn to {original_date}")
                    except Exception as e:
                        logger.warning(f"Could not set SentOn date: {e}")

                else:
                    # This is a received item - copy received item properties
                    if hasattr(mail_item, 'SenderName') and mail_item.SenderName:
                        new_item.SenderName = mail_item.SenderName
                    if hasattr(mail_item, 'SenderEmailAddress') and mail_item.SenderEmailAddress:
                        new_item.SenderEmailAddress = mail_item.SenderEmailAddress
                    if hasattr(mail_item, 'To') and mail_item.To:
                        new_item.To = mail_item.To
                    if hasattr(mail_item, 'CC') and mail_item.CC:
                        new_item.CC = mail_item.CC
                    if hasattr(mail_item, 'BCC') and mail_item.BCC:
                        new_item.BCC = mail_item.BCC

                    # NOTE: Setting ReceivedTime for new items may not work reliably
                    # Outlook often overrides this with current time when saving
                    # The original date is preserved in the email body header
                    try:
                        if original_date:
                            new_item.ReceivedTime = original_date
                            logger.debug(f"Attempted to set ReceivedTime to {original_date}")
                    except Exception as e:
                        logger.warning(f"Could not set ReceivedTime date: {e}")

                # Copy other common properties
                if hasattr(mail_item, 'Importance'):
                    new_item.Importance = mail_item.Importance
                if hasattr(mail_item, 'Sensitivity'):
                    new_item.Sensitivity = mail_item.Sensitivity
                if hasattr(mail_item, 'Categories') and mail_item.Categories:
                    new_item.Categories = mail_item.Categories

            except Exception as e:
                logger.warning(f"Some properties could not be set for {result['file_name']}: {e}")
            
            # Copy attachments
            try:
                if hasattr(mail_item, 'Attachments') and mail_item.Attachments.Count > 0:
                    for i in range(1, mail_item.Attachments.Count + 1):
                        attachment = mail_item.Attachments.Item(i)
                        temp_path = os.path.join(os.path.expanduser("~"), "temp", f"temp_attachment_{i}_{attachment.FileName}")
                        os.makedirs(os.path.dirname(temp_path), exist_ok=True)
                        
                        attachment.SaveAsFile(temp_path)
                        new_item.Attachments.Add(temp_path)
                        
                        # Clean up temp file
                        try:
                            os.remove(temp_path)
                        except:
                            pass
            except Exception as e:
                logger.warning(f"Could not copy attachments for {result['file_name']}: {e}")
            
            # Save the new item
            new_item.Save()
            
            # Move to target folder if not already there
            try:
                moved_item = new_item.Move(target_folder)
                logger.info(f"Successfully imported {result['file_name']} to {classification}")
            except Exception as e:
                logger.warning(f"Created in Inbox but failed to move to {classification}: {e}")
            
            # Clean up
            mail_item.Close(0)  # Close without saving
            del mail_item
            del new_item
            
            result['success'] = True
            
        except Exception as e:
            error_msg = f"Error importing {result['file_name']}: {str(e)}"
            logger.error(error_msg)
            result['error'] = error_msg
        
        result['import_time'] = time.time() - start_time
        return result
    
    def import_email_files(self, email_files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Import all email files with controlled delays"""
        results = {
            'total_files': len(email_files),
            'successful_imports': 0,
            'failed_imports': 0,
            'import_results': [],
            'total_time': 0,
            'folder_stats': {}
        }
        
        start_time = time.time()
        logger.info(f"Starting import of {len(email_files)} email files...")
        
        for i, file_info in enumerate(email_files, 1):
            file_path = file_info['file_path']
            
            logger.info(f"Importing {i}/{len(email_files)}: {os.path.basename(file_path)}")
            
            # Import the file
            import_result = self.import_single_msg_file(file_path, file_info)
            results['import_results'].append(import_result)
            
            if import_result['success']:
                results['successful_imports'] += 1
                classification = import_result['classification']
                if classification not in results['folder_stats']:
                    results['folder_stats'][classification] = 0
                results['folder_stats'][classification] += 1
            else:
                results['failed_imports'] += 1
            
            # Progress update
            if i % 10 == 0 or i == len(email_files):
                elapsed = time.time() - start_time
                rate = i / elapsed if elapsed > 0 else 0
                eta = (len(email_files) - i) / rate if rate > 0 else 0
                logger.info(f"Progress: {i}/{len(email_files)} ({i/len(email_files)*100:.1f}%) "
                          f"- Rate: {rate:.1f} files/sec - ETA: {eta:.0f}s")
            
            # Controlled delay between imports to prevent overwhelming Outlook
            if i < len(email_files):  # Don't delay after the last file
                time.sleep(self.delay_between_imports)
        
        results['total_time'] = time.time() - start_time
        
        logger.info(f"Import completed: {results['successful_imports']} successful, "
                   f"{results['failed_imports']} failed in {results['total_time']:.2f} seconds")
        
        return results
    
    def cleanup(self):
        """Clean up COM objects"""
        try:
            if self.namespace:
                del self.namespace
            if self.outlook:
                del self.outlook
            pythoncom.CoUninitialize()
        except:
            pass

def load_analysis_results(analysis_file: str) -> tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Load analysis results and filter for email files only"""
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Filter for email files only
        email_files = [result for result in data['results']
                      if result['has_email_conversations'] and not result.get('error')]

        # Get import settings if available
        import_settings = data.get('import_settings', {})

        logger.info(f"Loaded {len(email_files)} email files from analysis results")
        return email_files, import_settings

    except Exception as e:
        logger.error(f"Failed to load analysis results: {e}")
        return [], {}

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python import_email_files.py <analysis_results.json> [outlook_folder_name] [delay_seconds]")
        print("Example: python import_email_files.py msg_analysis_results.json My_Imported_Emails 0.5")
        sys.exit(1)

    analysis_file = sys.argv[1]

    try:
        # Load email files from analysis results
        email_files, import_settings = load_analysis_results(analysis_file)

        if not email_files:
            logger.warning("No email files to import!")
            return

        # Get import parameters (from command line or saved settings)
        outlook_folder_name = (sys.argv[2] if len(sys.argv) > 2
                              else import_settings.get('outlook_folder', 'Imported_MSG_Files'))
        delay_seconds = (float(sys.argv[3]) if len(sys.argv) > 3
                        else import_settings.get('delay_seconds', 0.5))

        print(f"Importing {len(email_files)} email files to Outlook folder: {outlook_folder_name}")
        print(f"Using {delay_seconds} second delays between imports")
        print("=" * 60)

        # Initialize importer
        importer = OutlookImporter(outlook_folder_name, delay_seconds)

        if not importer.initialize_outlook():
            logger.error("Failed to initialize Outlook")
            sys.exit(1)

        try:
            # Import files
            results = importer.import_email_files(email_files)

            # Save import results
            output_file = f"import_results_{int(time.time())}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            # Print summary
            print(f"\n{'='*50}")
            print("MSG FILE IMPORT SUMMARY")
            print(f"{'='*50}")
            print(f"Total files processed: {results['total_files']}")
            print(f"Successful imports: {results['successful_imports']}")
            print(f"Failed imports: {results['failed_imports']}")
            print(f"Import time: {results['total_time']:.2f} seconds")
            if results['total_time'] > 0:
                print(f"Import rate: {results['successful_imports']/results['total_time']:.1f} files/second")
            print(f"Delay between imports: {delay_seconds} seconds")
            print(f"\nFolder distribution:")
            for folder, count in results['folder_stats'].items():
                print(f"  {folder}: {count} files")
            print(f"\nResults saved to: {output_file}")
            print(f"Log file: msg_import.log")
            print(f"\n✅ Import completed! Check your Outlook folder: {outlook_folder_name}")

        finally:
            importer.cleanup()

    except Exception as e:
        logger.error(f"Import failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

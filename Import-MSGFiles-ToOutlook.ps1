# Import MSG Files to Outlook with Folder Organization
# This script imports MSG files into Outlook and organizes them based on metadata

param(
    [Parameter(Mandatory=$false)]
    [string]$MSGFolderPath,

    [Parameter(Mandatory=$false)]
    [string]$OutlookRootFolderName,

    [Parameter(Mandatory=$false)]
    [int]$BatchSize = 100,

    [Parameter(Mandatory=$false)]
    [int]$DelayBetweenBatches = 2
)

# Function to write colored output
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Function to get user input with validation
function Get-UserInput {
    param(
        [string]$Prompt,
        [bool]$Required = $true
    )

    do {
        $userInput = Read-Host $Prompt

        if ($Required -and [string]::IsNullOrWhiteSpace($userInput)) {
            Write-ColorOutput "This field is required. Please enter a value." "Red"
        }
    } while ($Required -and [string]::IsNullOrWhiteSpace($userInput))

    return $userInput.Trim()
}

# Function to move skipped MSG files to skipped_msg folder
function Move-SkippedMSGFile {
    param([string]$SourcePath, [string]$SkippedFolderPath, [string]$Reason)

    try {
        # Create skipped_msg folder if it doesn't exist
        if (-not (Test-Path $SkippedFolderPath)) {
            New-Item -ItemType Directory -Path $SkippedFolderPath -Force | Out-Null
            Write-ColorOutput "  -> Created skipped_msg folder: $SkippedFolderPath" "Cyan"
        }

        $fileName = Split-Path $SourcePath -Leaf
        $destinationPath = Join-Path $SkippedFolderPath $fileName

        # Handle duplicate names
        $counter = 1
        $originalDestination = $destinationPath
        while (Test-Path $destinationPath) {
            $nameWithoutExt = [System.IO.Path]::GetFileNameWithoutExtension($originalDestination)
            $extension = [System.IO.Path]::GetExtension($originalDestination)
            $destinationPath = Join-Path $SkippedFolderPath "$nameWithoutExt`_$counter$extension"
            $counter++
        }

        # Retry move operation with delays to handle file locking
        $maxRetries = 5
        $retryCount = 0
        $moveSuccess = $false

        while (-not $moveSuccess -and $retryCount -lt $maxRetries) {
            try {
                # Test if file is accessible
                $fileStream = [System.IO.File]::Open($SourcePath, 'Open', 'Read', 'None')
                $fileStream.Close()
                $fileStream.Dispose()

                # File is accessible, try to move it
                Move-Item -Path $SourcePath -Destination $destinationPath -Force -ErrorAction Stop
                $moveSuccess = $true
                Write-ColorOutput "  -> Moved to skipped_msg: $fileName" "Yellow"

            } catch {
                $retryCount++
                if ($retryCount -lt $maxRetries) {
                    Write-ColorOutput "  -> File locked, retrying in $($retryCount * 200)ms... (attempt $retryCount/$maxRetries)" "Gray"
                    Start-Sleep -Milliseconds ($retryCount * 200)
                } else {
                    Write-ColorOutput "  -> ERROR: Failed to move after $maxRetries attempts: $($_.Exception.Message)" "Red"
                    return $false
                }
            }
        }

        if ($moveSuccess) {
            return $true
        } else {
            return $false
        }

    } catch {
        Write-ColorOutput "  -> ERROR: Failed to move MSG file: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Function to create folder if it doesn't exist
function Ensure-OutlookFolder {
    param(
        [object]$ParentFolder,
        [string]$FolderName
    )
    
    try {
        $existingFolder = $ParentFolder.Folders | Where-Object { $_.Name -eq $FolderName }
        if ($existingFolder) {
            return $existingFolder
        } else {
            Write-ColorOutput "Creating folder: $FolderName" "Yellow"
            return $ParentFolder.Folders.Add($FolderName)
        }
    } catch {
        Write-ColorOutput "Error creating folder $FolderName : $($_.Exception.Message)" "Red"
        return $null
    }
}

# Function to analyze ALL embedded items in MSG file
function Analyze-MSGFileContent {
    param([object]$MailItem, [string]$FileName)

    $analysisResult = @{
        HasEmailConversations = $false
        EmailItems = @()
        NonEmailItems = @()
        SkipReasons = @()
        TotalItems = 0
    }

    try {
        Write-ColorOutput "  -> Analyzing MSG file content..." "Cyan"

        # Analyze the main item
        $mainMessageClass = $MailItem.MessageClass
        $mainSubject = $MailItem.Subject
        $mainBody = $MailItem.Body

        Write-ColorOutput "    Main MessageClass: $mainMessageClass" "DarkGray"
        Write-ColorOutput "    Main Subject: $mainSubject" "DarkGray"

        $analysisResult.TotalItems++

        # Check if main item is an email conversation
        if ($mainMessageClass -match "IPM\.Note") {
            if (-not [string]::IsNullOrWhiteSpace($mainBody)) {
                $analysisResult.HasEmailConversations = $true
                $analysisResult.EmailItems += @{
                    MessageClass = $mainMessageClass
                    Subject = $mainSubject
                    Body = $mainBody
                    IsMain = $true
                }
                Write-ColorOutput "    -> FOUND: Email conversation in main item" "Green"
            } else {
                $analysisResult.NonEmailItems += "Main item: Empty email body"
                Write-ColorOutput "    -> Empty email body in main item" "Yellow"
            }
        } else {
            $analysisResult.NonEmailItems += "Main item: $mainMessageClass"
            Write-ColorOutput "    -> Non-email main item: $mainMessageClass" "Yellow"
        }

        # Check for embedded items (attachments that might be MSG files or other items)
        try {
            if ($MailItem.Attachments.Count -gt 0) {
                Write-ColorOutput "    -> Found $($MailItem.Attachments.Count) attachments, checking for embedded items..." "Cyan"

                for ($i = 1; $i -le $MailItem.Attachments.Count; $i++) {
                    try {
                        $attachment = $MailItem.Attachments.Item($i)
                        if ($attachment.Type -eq 5) { # olEmbeddeditem = 5
                            $embeddedItem = $attachment.EmbeddedItem
                            if ($embeddedItem) {
                                $embeddedClass = $embeddedItem.MessageClass
                                $embeddedSubject = $embeddedItem.Subject
                                $embeddedBody = $embeddedItem.Body

                                Write-ColorOutput "      Embedded MessageClass: $embeddedClass" "DarkGray"
                                $analysisResult.TotalItems++

                                if ($embeddedClass -match "IPM\.Note") {
                                    if (-not [string]::IsNullOrWhiteSpace($embeddedBody)) {
                                        $analysisResult.HasEmailConversations = $true
                                        $analysisResult.EmailItems += @{
                                            MessageClass = $embeddedClass
                                            Subject = $embeddedSubject
                                            Body = $embeddedBody
                                            IsMain = $false
                                        }
                                        Write-ColorOutput "      -> FOUND: Email conversation in embedded item" "Green"
                                    } else {
                                        $analysisResult.NonEmailItems += "Embedded item: Empty email body"
                                    }
                                } else {
                                    $analysisResult.NonEmailItems += "Embedded item: $embeddedClass"
                                }

                                try { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($embeddedItem) | Out-Null } catch { }
                            }
                        }
                        try { [System.Runtime.Interopservices.Marshal]::ReleaseComObject($attachment) | Out-Null } catch { }
                    } catch {
                        Write-ColorOutput "      -> Error analyzing attachment $i : $($_.Exception.Message)" "Red"
                    }
                }
            }
        } catch {
            Write-ColorOutput "    -> Error checking attachments: $($_.Exception.Message)" "Yellow"
        }

        # Determine final result
        if ($analysisResult.HasEmailConversations) {
            Write-ColorOutput "  -> RESULT: MSG file contains email conversations - WILL IMPORT" "Green"
        } else {
            $skipReason = "No email conversations found. Contains: " + ($analysisResult.NonEmailItems -join ", ")
            $analysisResult.SkipReasons += $skipReason
            Write-ColorOutput "  -> RESULT: MSG file contains no email conversations - WILL SKIP" "Yellow"
        }

        return $analysisResult

    } catch {
        Write-ColorOutput "  -> ERROR: Failed to analyze MSG file: $($_.Exception.Message)" "Red"
        $analysisResult.SkipReasons += "Analysis error: $($_.Exception.Message)"
        return $analysisResult
    }
}

# Function to classify MSG file based on metadata
function Get-MSGClassification {
    param([object]$MailItem)

    try {
        $messageClass = $MailItem.MessageClass
        $subject = $MailItem.Subject
        $senderName = $MailItem.SenderName

        Write-ColorOutput "  MessageClass: $messageClass" "DarkGray"

        # Classification logic based on MessageClass and other properties
        switch -Regex ($messageClass) {
            "IPM\.Note\.Rules\.ReplyTemplate" { return "Rules" }
            "IPM\.Note\.Rules" { return "Rules" }
            "IPM\.Schedule\.Meeting\.Request" { return "Calendar" }
            "IPM\.Schedule\.Meeting\.Resp" { return "Calendar" }
            "IPM\.Schedule\.Meeting\.Canceled" { return "Calendar" }
            "IPM\.Schedule\.Meeting" { return "Calendar" }
            "IPM\.Appointment" { return "Calendar" }
            "IPM\.Task" { return "Tasks" }
            "IPM\.Contact" { return "Contacts" }
            "IPM\.DistList" { return "Contacts" }
            "IPM\.StickyNote" { return "Notes" }
            "IPM\.Post" { return "Posts" }
            "IPM\.Note\.SMIME" { return "Secure" }
            "IPM\.Note\.Secure" { return "Secure" }
            "IPM\.Note\.Receipt" { return "Receipts" }
            "IPM\.Note\.Delivery" { return "Receipts" }
            "IPM\.Note\.NonDelivery" { return "Receipts" }
            "IPM\.Note\.Read" { return "Receipts" }
            "IPM\.Note\.Unread" { return "Receipts" }
            "IPM\.Note\.Custom" { return "Custom" }
            "IPM\.OLE\.Class" { return "Attachments" }
            default {
                # For regular emails (IPM.Note), try to determine folder based on properties
                try {
                    # Check if it's in Sent Items based on properties
                    if ($MailItem.Sent -eq $true) {
                        return "Sent Items"
                    }

                    # Check if it's a draft (not sent and has no received time)
                    if (-not $MailItem.Sent -and -not $MailItem.ReceivedTime) {
                        return "Drafts"
                    }

                    # Check if it's deleted (if we can access that property)
                    try {
                        if ($MailItem.Parent.Name -match "Deleted|Trash") {
                            return "Deleted Items"
                        }
                    } catch { }

                    # Check subject patterns for additional classification
                    if ($subject) {
                        if ($subject -match "^(Out of Office|Automatic Reply|Auto-Reply)") {
                            return "Auto-Replies"
                        }
                        if ($subject -match "^(Delivery Status Notification|Undeliverable|Mail Delivery)") {
                            return "Receipts"
                        }
                        if ($subject -match "^(Junk|SPAM|\[SPAM\])") {
                            return "Junk Email"
                        }
                    }

                    # Default to Inbox for received emails
                    if ($MailItem.ReceivedTime) {
                        return "Inbox"
                    }

                    # If we can't determine, put in Unclassified
                    return "Unclassified"

                } catch {
                    return "Unclassified"
                }
            }
        }
    } catch {
        Write-ColorOutput "Error classifying message: $($_.Exception.Message)" "Red"
        return "Unclassified"
    }
}



# Get user input if parameters not provided
Write-ColorOutput "=== MSG File Import to Outlook ===" "Cyan"
Write-ColorOutput ""

# Prompt for MSG folder path if not provided
if ([string]::IsNullOrWhiteSpace($MSGFolderPath)) {
    Write-ColorOutput "Please specify the full folder path containing your MSG files:" "Yellow"
    Write-ColorOutput "Examples:" "Gray"
    Write-ColorOutput "  C:\Users\<USER>\Downloads\MSG_Files" "Gray"
    Write-ColorOutput "  .\extracted_email (for relative path)" "Gray"
    Write-ColorOutput "  D:\Email_Backup\MSG_Files" "Gray"
    $MSGFolderPath = Get-UserInput "MSG Folder Path" $true
}

# Prompt for Outlook folder name if not provided
if ([string]::IsNullOrWhiteSpace($OutlookRootFolderName)) {
    Write-ColorOutput "`nPlease specify the name for the main folder in Outlook where emails will be imported:" "Yellow"
    Write-ColorOutput "This will create subfolders like 'Calendar', 'Sent Items', etc. inside this main folder." "Gray"
    Write-ColorOutput "Examples: My_Imported_Emails, Work_Archive_2025, Personal_Backup" "Gray"
    $OutlookRootFolderName = Get-UserInput "Outlook Folder Name" $true
}

# Validate MSG folder exists
if (-not (Test-Path $MSGFolderPath)) {
    Write-ColorOutput "Error: MSG folder path '$MSGFolderPath' does not exist!" "Red"
    Write-ColorOutput ""
    Write-ColorOutput "Please check the following:" "Yellow"
    Write-ColorOutput "  1. Verify the folder path is correct" "Gray"
    Write-ColorOutput "  2. Make sure you have permission to access the folder" "Gray"
    Write-ColorOutput "  3. Use full path (e.g., C:\Users\<USER>\Downloads\MSG_Files)" "Gray"
    Write-ColorOutput "  4. For relative paths, ensure the folder exists relative to current directory" "Gray"
    Write-ColorOutput ""
    Write-ColorOutput "Current directory: $(Get-Location)" "Cyan"
    exit 1
}

# Count MSG files
try {
    $msgFiles = Get-ChildItem -Path $MSGFolderPath -Filter "*.msg" -ErrorAction Stop
    if ($msgFiles.Count -eq 0) {
        Write-ColorOutput "Error: No MSG files found in '$MSGFolderPath'!" "Red"
        Write-ColorOutput ""
        Write-ColorOutput "Please ensure:" "Yellow"
        Write-ColorOutput "  1. The folder contains .msg files" "Gray"
        Write-ColorOutput "  2. The files have the .msg extension" "Gray"
        Write-ColorOutput "  3. You have permission to read the files" "Gray"
        exit 1
    }
} catch {
    Write-ColorOutput "Error accessing folder '$MSGFolderPath': $($_.Exception.Message)" "Red"
    exit 1
}

# Show summary and ask for confirmation
Write-ColorOutput "`n=== Import Summary ===" "Green"
Write-ColorOutput "MSG Files Found: $($msgFiles.Count)" "White"
Write-ColorOutput "Source Folder: $MSGFolderPath" "White"
Write-ColorOutput "Outlook Folder: $OutlookRootFolderName" "White"
Write-ColorOutput "Batch Size: $BatchSize" "White"
Write-ColorOutput ""

$confirmation = Get-UserInput "Do you want to proceed with the import? (y/n)" $true
if ($confirmation.ToLower() -ne "y" -and $confirmation.ToLower() -ne "yes") {
    Write-ColorOutput "Import cancelled by user." "Yellow"
    exit 0
}

Write-ColorOutput ""

# Main script execution
try {
    Write-ColorOutput "Starting MSG Import Process..." "Green"
    Write-ColorOutput "Source Folder: $MSGFolderPath" "Cyan"
    Write-ColorOutput "Target Outlook Folder: $OutlookRootFolderName" "Cyan"
    Write-ColorOutput "Batch Size: $BatchSize" "Cyan"
    
    # Validate source folder
    if (-not (Test-Path $MSGFolderPath)) {
        throw "Source folder does not exist: $MSGFolderPath"
    }
    
    # Get all MSG files
    $msgFiles = Get-ChildItem -Path $MSGFolderPath -Filter "*.msg" -Recurse
    $totalFiles = $msgFiles.Count
    
    if ($totalFiles -eq 0) {
        Write-ColorOutput "No MSG files found in the specified folder." "Yellow"
        exit
    }
    
    Write-ColorOutput "Found $totalFiles MSG files to process" "Green"
    
    # Initialize Outlook COM object
    Write-ColorOutput "Connecting to Outlook..." "Yellow"
    $outlook = New-Object -ComObject Outlook.Application
    $namespace = $outlook.GetNamespace("MAPI")
    
    # Get the default store (usually the main mailbox)
    $defaultStore = $namespace.DefaultStore
    $rootFolder = $defaultStore.GetRootFolder()
    
    # Create or get the main import folder
    $mainImportFolder = Ensure-OutlookFolder -ParentFolder $rootFolder -FolderName $OutlookRootFolderName
    if (-not $mainImportFolder) {
        throw "Failed to create main import folder"
    }
    
    # Initialize counters and statistics
    $processedCount = 0
    $successCount = 0
    $errorCount = 0
    $skippedCount = 0
    $importedCount = 0
    $folderStats = @{}
    $errorLog = @()
    $filesToMove = @()  # List of files to move to skipped_msg folder

    # Create log files
    $logFile = Join-Path (Split-Path $MSGFolderPath -Parent) "MSG_Import_Log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    $skipLogFile = Join-Path (Split-Path $MSGFolderPath -Parent) "MSG_Skipped_Files_Log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

    "MSG Import Log - Started at $(Get-Date)" | Out-File -FilePath $logFile -Encoding UTF8
    "MSG Skipped Files Log - Started at $(Get-Date)" | Out-File -FilePath $skipLogFile -Encoding UTF8
    "=" * 80 | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
    "SKIPPED MSG FILES - NO EMAIL CONVERSATIONS FOUND" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
    "=" * 80 | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
    "" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
    
    # Process files in batches
    for ($i = 0; $i -lt $totalFiles; $i += $BatchSize) {
        $batchNumber = [math]::Floor($i / $BatchSize) + 1
        $batchEnd = [math]::Min($i + $BatchSize - 1, $totalFiles - 1)
        
        Write-ColorOutput "`nProcessing Batch $batchNumber (Files $($i+1) to $($batchEnd+1))..." "Magenta"
        
        $batch = $msgFiles[$i..$batchEnd]
        
        foreach ($msgFile in $batch) {
            $processedCount++
            $fileName = $msgFile.Name
            
            try {
                # Progress indicator
                $percentComplete = [math]::Round(($processedCount / $totalFiles) * 100, 1)
                Write-ColorOutput "Processing: $fileName ($processedCount/$totalFiles - $percentComplete%)" "White"

                # Log to file
                "Processing: $fileName" | Out-File -FilePath $logFile -Append -Encoding UTF8

                # Validate file exists before processing
                if (-not (Test-Path $msgFile.FullName)) {
                    throw "File not found: $($msgFile.FullName)"
                }

                # Check file path length (Windows has 260 character limit)
                if ($msgFile.FullName.Length -gt 250) {
                    Write-ColorOutput "  -> WARNING: File path is very long ($($msgFile.FullName.Length) chars)" "Yellow"
                    Write-ColorOutput "  -> Path: $($msgFile.FullName)" "Gray"
                }

                # Open the MSG file using the correct method for MSG files
                $mailItem = $namespace.OpenSharedItem($msgFile.FullName)

                # Analyze ALL content in the MSG file (main item + embedded items)
                $analysisResult = Analyze-MSGFileContent -MailItem $mailItem -FileName $fileName

                if (-not $analysisResult.HasEmailConversations) {
                    # No email conversations found - skip and move to skipped_msg folder
                    $skipReason = $analysisResult.SkipReasons -join "; "
                    Write-ColorOutput "  -> SKIPPING: $skipReason" "Yellow"

                    # Log to main log file
                    "SKIPPED: $fileName - $skipReason" | Out-File -FilePath $logFile -Append -Encoding UTF8

                    # Log detailed skip information to dedicated skip log file
                    "File: $fileName" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                    "Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                    "Reason: $skipReason" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                    "Total Items in MSG: $($analysisResult.TotalItems)" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                    if ($analysisResult.NonEmailItems.Count -gt 0) {
                        "Non-Email Items Found:" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                        foreach ($item in $analysisResult.NonEmailItems) {
                            "  - $item" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                        }
                    }
                    "-" * 50 | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                    "" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8

                    # Update skip statistics
                    $skippedCount++
                    if (-not $folderStats.ContainsKey("SKIPPED")) {
                        $folderStats["SKIPPED"] = 0
                    }
                    $folderStats["SKIPPED"]++

                    # Clean up COM object thoroughly and wait for release
                    try {
                        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mailItem) | Out-Null
                        $mailItem = $null
                        # Force garbage collection to ensure COM object is released
                        [System.GC]::Collect()
                        [System.GC]::WaitForPendingFinalizers()
                        # Small delay to ensure file handle is released
                        Start-Sleep -Milliseconds 100
                    } catch { }

                    # Add to list of files to move (will be moved at end of batch)
                    $filesToMove += @{
                        SourcePath = $msgFile.FullName
                        FileName = $fileName
                        Reason = $skipReason
                    }

                    Write-ColorOutput "  -> Marked for moving to skipped_msg folder" "Yellow"
                    "MARKED_FOR_MOVE: $fileName - $skipReason" | Out-File -FilePath $logFile -Append -Encoding UTF8

                    continue
                }

                # MSG file contains email conversations - proceed with import
                Write-ColorOutput "  -> IMPORTING: Found $($analysisResult.EmailItems.Count) email conversation(s)" "Green"
                "IMPORTING: $fileName - Found $($analysisResult.EmailItems.Count) email conversation(s)" | Out-File -FilePath $logFile -Append -Encoding UTF8

                # Classify the email message for folder organization
                $classification = Get-MSGClassification -MailItem $mailItem

                # Update statistics
                if (-not $folderStats.ContainsKey($classification)) {
                    $folderStats[$classification] = 0
                }
                $folderStats[$classification]++

                # Create or get the classification folder
                $targetFolder = Ensure-OutlookFolder -ParentFolder $mainImportFolder -FolderName $classification

                if ($targetFolder) {
                    # Create the item in the Inbox first, then move it to target folder
                    $inbox = $namespace.GetDefaultFolder(6)  # olFolderInbox = 6
                    $newItem = $inbox.Items.Add(0)  # 0 = MailItem

                    # Subject will be set below after determining original date

                    # Collect original date information for the body header
                    $originalDateInfo = ""
                    $originalDate = $null

                    # For email items, get ReceivedTime or SentOn
                    if ($mailItem.MessageClass -like "*Note*") {
                        if ($mailItem.ReceivedTime) {
                            $formattedDate = $mailItem.ReceivedTime.ToString("dd MMM yyyy HH:mm:ss")
                            $originalDateInfo += "Original Received Date: $formattedDate`r`n"
                            $originalDate = $mailItem.ReceivedTime
                        }
                        if ($mailItem.SentOn) {
                            $formattedDate = $mailItem.SentOn.ToString("dd MMM yyyy HH:mm:ss")
                            $originalDateInfo += "Original Sent Date: $formattedDate`r`n"
                            if (-not $originalDate) { $originalDate = $mailItem.SentOn }
                        }
                    }
                    # For appointment items, get Start/End dates
                    elseif ($mailItem.MessageClass -like "*Appointment*") {
                        if ($mailItem.Start) {
                            $formattedDate = $mailItem.Start.ToString("dd MMM yyyy HH:mm:ss")
                            $originalDateInfo += "Original Appointment Start: $formattedDate`r`n"
                            $originalDate = $mailItem.Start
                        }
                        if ($mailItem.End) {
                            $formattedDate = $mailItem.End.ToString("dd MMM yyyy HH:mm:ss")
                            $originalDateInfo += "Original Appointment End: $formattedDate`r`n"
                        }
                        if ($mailItem.Location) {
                            $originalDateInfo += "Original Location: $($mailItem.Location)`r`n"
                        }
                    }

                    # Add comprehensive header to the body with original dates prominently displayed
                    $newItem.Body = "=== IMPORTED MSG FILE ===`r`n" +
                                   "Original MessageClass: $($mailItem.MessageClass)`r`n" +
                                   $originalDateInfo +
                                   "Import Date: $(Get-Date)`r`n" +
                                   "Classification: $classification`r`n" +
                                   "Original File: $fileName`r`n" +
                                   "=========================`r`n`r`n" +
                                   $mailItem.Body

                    # Keep original subject clean and simple
                    $newItem.Subject = $mailItem.Subject

                    # Log the original date for reference
                    if ($originalDate) {
                        $dateStr = $originalDate.ToString("dd MMM yyyy HH:mm:ss")
                        Write-ColorOutput "  -> Original date: $dateStr" "DarkGray"
                    }

                    # Set mail properties to preserve original dates and details
                    try {
                        # Determine the best original date to use
                        $originalDate = $null

                        # For email items, use ReceivedTime or SentOn (these contain original dates)
                        if ($mailItem.MessageClass -like "*Note*") {
                            if ($mailItem.ReceivedTime) {
                                $originalDate = $mailItem.ReceivedTime
                                $newItem.ReceivedTime = $mailItem.ReceivedTime
                                Write-ColorOutput "  -> Using original received date: $($mailItem.ReceivedTime)" "DarkGray"
                            } elseif ($mailItem.SentOn) {
                                $originalDate = $mailItem.SentOn
                                $newItem.SentOn = $mailItem.SentOn
                                Write-ColorOutput "  -> Using original sent date: $($mailItem.SentOn)" "DarkGray"
                            }
                        }
                        # For appointment items, use Start date as reference
                        elseif ($mailItem.MessageClass -like "*Appointment*") {
                            if ($mailItem.Start) {
                                $originalDate = $mailItem.Start
                                # For appointments imported as emails, set received time to appointment start
                                $newItem.ReceivedTime = $mailItem.Start
                                Write-ColorOutput "  -> Using appointment start date: $($mailItem.Start)" "DarkGray"
                            }
                        }

                        # If we found an original date, also try to set creation time
                        if ($originalDate) {
                            try {
                                $newItem.CreationTime = $originalDate
                            } catch {
                                # CreationTime might be read-only, ignore error
                            }
                        }

                        # Set sender/recipient information
                        if ($classification -eq "Sent Items") {
                            $newItem.To = if ($mailItem.To) { $mailItem.To } else { "<EMAIL>" }
                            if ($mailItem.SenderName) { $newItem.SenderName = $mailItem.SenderName }
                            if ($mailItem.SenderEmailAddress) { $newItem.SenderEmailAddress = $mailItem.SenderEmailAddress }
                            # Ensure we have a sent date
                            if (-not $newItem.SentOn -and $originalDate) {
                                $newItem.SentOn = $originalDate
                            }
                        } else {
                            $newItem.SenderName = if ($mailItem.SenderName) { $mailItem.SenderName } else { "Imported Sender" }
                            $newItem.SenderEmailAddress = if ($mailItem.SenderEmailAddress) { $mailItem.SenderEmailAddress } else { "<EMAIL>" }
                            if ($mailItem.To) { $newItem.To = $mailItem.To }
                            # Ensure we have a received date
                            if (-not $newItem.ReceivedTime -and $originalDate) {
                                $newItem.ReceivedTime = $originalDate
                            }
                        }

                        # Set importance
                        if ($mailItem.Importance) { $newItem.Importance = $mailItem.Importance }

                    } catch {
                        Write-ColorOutput "  Warning: Some properties could not be set: $($_.Exception.Message)" "Yellow"
                    }

                    # Copy attachments if any
                    try {
                        if ($mailItem.Attachments.Count -gt 0) {
                            for ($i = 1; $i -le $mailItem.Attachments.Count; $i++) {
                                $attachment = $mailItem.Attachments.Item($i)
                                $tempPath = [System.IO.Path]::GetTempFileName() + "_" + $attachment.FileName
                                $attachment.SaveAsFile($tempPath)
                                $newItem.Attachments.Add($tempPath)
                                Remove-Item $tempPath -Force -ErrorAction SilentlyContinue
                            }
                        }
                    } catch {
                        Write-ColorOutput "  Warning: Could not copy attachments: $($_.Exception.Message)" "Yellow"
                    }

                    # Save the item first
                    $newItem.Save()

                    # Now move it to the target folder
                    try {
                        $movedItem = $newItem.Move($targetFolder)
                        Write-ColorOutput "  -> Successfully moved to: $classification" "Green"
                    } catch {
                        Write-ColorOutput "  -> Created in Inbox, but failed to move to $classification : $($_.Exception.Message)" "Yellow"
                        Write-ColorOutput "  -> Item remains in Inbox with [IMPORTED-$classification] prefix" "Yellow"
                    }

                    Write-ColorOutput "  -> Imported to: $classification" "Green"
                    "  SUCCESS: Imported to $classification" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    $successCount++
                    $importedCount++

                    # Clean up the new item
                    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($newItem) | Out-Null
                } else {
                    $errorMsg = "Failed to create/access folder: $classification"
                    Write-ColorOutput "  -> $errorMsg" "Red"
                    "  ERROR: $errorMsg" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    $errorLog += "$fileName : $errorMsg"
                    $errorCount++
                }

                # Clean up
                $mailItem.Close(0)  # Close without saving
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($mailItem) | Out-Null

            } catch {
                $errorMsg = "Error processing $fileName : $($_.Exception.Message)"
                $fullPath = $msgFile.FullName
                Write-ColorOutput "  -> $errorMsg" "Red"
                Write-ColorOutput "  -> Full path: $fullPath" "Gray"
                Write-ColorOutput "  -> Path length: $($fullPath.Length) characters" "Gray"
                Write-ColorOutput "  -> File exists: $(Test-Path $fullPath)" "Gray"

                "  ERROR: $errorMsg" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "  ERROR: Full path: $fullPath" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "  ERROR: Path length: $($fullPath.Length) characters" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "  ERROR: File exists: $(Test-Path $fullPath)" | Out-File -FilePath $logFile -Append -Encoding UTF8

                $errorLog += $errorMsg
                $errorCount++
            }
        }

        # Move skipped files at the end of each batch (when COM objects are released)
        if ($filesToMove.Count -gt 0) {
            Write-ColorOutput "`nMoving $($filesToMove.Count) skipped files to skipped_msg folder..." "Cyan"
            $skippedFolderPath = Join-Path (Split-Path $MSGFolderPath -Parent) "skipped_msg"

            foreach ($fileToMove in $filesToMove) {
                $moveSuccess = Move-SkippedMSGFile -SourcePath $fileToMove.SourcePath -SkippedFolderPath $skippedFolderPath -Reason $fileToMove.Reason

                if ($moveSuccess) {
                    Write-ColorOutput "  -> Moved: $($fileToMove.FileName)" "Yellow"
                    "MOVED: $($fileToMove.FileName) to skipped_msg folder" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    "MOVED TO SKIPPED_MSG: $($fileToMove.FileName)" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                } else {
                    Write-ColorOutput "  -> Failed to move: $($fileToMove.FileName)" "Red"
                    "MOVE_FAILED: $($fileToMove.FileName) - could not move to skipped_msg folder" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    "MOVE_FAILED: $($fileToMove.FileName) - could not move to skipped_msg folder" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
                }
            }

            # Clear the list for next batch
            $filesToMove = @()
        }

        # Delay between batches to prevent overwhelming Outlook
        if ($i + $BatchSize -lt $totalFiles) {
            Write-ColorOutput "Waiting $DelayBetweenBatches seconds before next batch..." "Yellow"
            Start-Sleep -Seconds $DelayBetweenBatches
        }
    }

    # Move any remaining skipped files
    if ($filesToMove.Count -gt 0) {
        Write-ColorOutput "`nMoving final $($filesToMove.Count) skipped files to skipped_msg folder..." "Cyan"
        $skippedFolderPath = Join-Path (Split-Path $MSGFolderPath -Parent) "skipped_msg"

        foreach ($fileToMove in $filesToMove) {
            $moveSuccess = Move-SkippedMSGFile -SourcePath $fileToMove.SourcePath -SkippedFolderPath $skippedFolderPath -Reason $fileToMove.Reason

            if ($moveSuccess) {
                Write-ColorOutput "  -> Moved: $($fileToMove.FileName)" "Yellow"
                "MOVED: $($fileToMove.FileName) to skipped_msg folder" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "MOVED TO SKIPPED_MSG: $($fileToMove.FileName)" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
            } else {
                Write-ColorOutput "  -> Failed to move: $($fileToMove.FileName)" "Red"
                "MOVE_FAILED: $($fileToMove.FileName) - could not move to skipped_msg folder" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "MOVE_FAILED: $($fileToMove.FileName) - could not move to skipped_msg folder" | Out-File -FilePath $skipLogFile -Append -Encoding UTF8
            }
        }
    }

    # Display final statistics
    Write-ColorOutput "`n=== IMPORT COMPLETED ===" "Green"
    $skippedCount = if ($folderStats.ContainsKey("SKIPPED")) { $folderStats["SKIPPED"] } else { 0 }
    $importedCount = $successCount

    Write-ColorOutput "Total MSG Files Found: $totalFiles" "Cyan"
    Write-ColorOutput "Successfully Imported: $importedCount emails" "Green"
    Write-ColorOutput "Skipped (Non-emails): $skippedCount items" "Yellow"
    Write-ColorOutput "Errors: $errorCount" "Red"
    Write-ColorOutput "Main Log File: $logFile" "Gray"
    if ($skippedCount -gt 0) {
        Write-ColorOutput "Skip Log File: $skipLogFile" "Gray"
    }

    Write-ColorOutput "`n=== IMPORTED EMAIL DISTRIBUTION ===" "Cyan"
    foreach ($folder in ($folderStats.Keys | Where-Object { $_ -ne "SKIPPED" } | Sort-Object)) {
        Write-ColorOutput "$folder : $($folderStats[$folder]) emails" "White"
    }

    if ($skippedCount -gt 0) {
        Write-ColorOutput "`n=== SKIPPED ITEMS ===" "Yellow"
        Write-ColorOutput "SKIPPED : $skippedCount MSG files (no email conversations found)" "Yellow"
        $skippedFolderPath = Join-Path (Split-Path $MSGFolderPath -Parent) "skipped_msg"
        Write-ColorOutput "MOVED TO: $skippedFolderPath" "Yellow"
        Write-ColorOutput "DETAILED SKIP LOG: $skipLogFile" "Gray"
    }

    # Log final statistics
    "`n=== FINAL STATISTICS ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
    "Total Files Processed: $processedCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
    "Successfully Imported: $successCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
    "Errors: $errorCount" | Out-File -FilePath $logFile -Append -Encoding UTF8

    if ($errorLog.Count -gt 0) {
        Write-ColorOutput "`n=== ERRORS SUMMARY ===" "Red"
        "`n=== ERRORS SUMMARY ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
        foreach ($errorEntry in $errorLog) {
            Write-ColorOutput $errorEntry "Red"
            $errorEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
        }
    }
    
} catch {
    Write-ColorOutput "Critical Error: $($_.Exception.Message)" "Red"
    Write-ColorOutput "Stack Trace: $($_.Exception.StackTrace)" "Red"
} finally {
    # Clean up COM objects
    if ($namespace) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($namespace) | Out-Null
    }
    if ($outlook) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($outlook) | Out-Null
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-ColorOutput "`nScript execution completed." "Green"
